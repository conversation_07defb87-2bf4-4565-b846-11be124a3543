package com.example.jubuddyai1

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.firebase.auth.FirebaseAuth
import com.example.jubuddyai1.ads.AdManager
import com.example.jubuddyai1.ads.NativeAdManager
import com.example.jubuddyai1.utils.LanguageManager

class SettingsActivity : BaseActivity() {

    private lateinit var backButton: ImageView
    private lateinit var profileName: TextView
    private lateinit var profileEmail: TextView
    private lateinit var languageMenuItem: LinearLayout
    private lateinit var termsMenuItem: LinearLayout
    private lateinit var signOutMenuItem: LinearLayout
    private val TAG = "SettingsActivity"
    private lateinit var adManager: AdManager
    private lateinit var nativeAdManager: NativeAdManager
    private val NATIVE_AD_ID = "ca-app-pub-3639460814030294/3631252078"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        // Hide system UI for immersive experience
        hideSystemUI()

        // Initialize views
        backButton = findViewById(R.id.backButton)
        profileName = findViewById(R.id.profileName)
        profileEmail = findViewById(R.id.profileEmail)
        languageMenuItem = findViewById(R.id.languageMenuItem)
        termsMenuItem = findViewById(R.id.termsMenuItem)
        signOutMenuItem = findViewById(R.id.signOutMenuItem)

        // Initialize AdManager
        adManager = AdManager(this)
        adManager.initialize()

        // Initialize the native ad manager
        nativeAdManager = NativeAdManager(this)

        // Load banner ad
        val adContainer = findViewById<FrameLayout>(R.id.adContainer)
        adManager.loadBannerAd(adContainer)

        // Load native ad
        nativeAdManager.loadNativeAd(adContainer, NATIVE_AD_ID)

        // Load user profile data
        loadUserProfile()

        // Set up click listeners
        setupClickListeners()
    }

    private fun loadUserProfile() {
        val sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
        val userName = sharedPreferences.getString("userName", "User")
        val userEmail = sharedPreferences.getString("userEmail", "<EMAIL>")

        profileName.text = userName ?: "User"
        profileEmail.text = userEmail ?: "<EMAIL>"
    }

    private fun setupClickListeners() {
        backButton.setOnClickListener {
            finish()
        }

        languageMenuItem.setOnClickListener {
            showLanguageSelectionDialog()
        }

        termsMenuItem.setOnClickListener {
            // Navigate to Terms & Conditions
            val intent = Intent(this, TermsActivity::class.java)
            startActivity(intent)
        }

        signOutMenuItem.setOnClickListener {
            signOut()
        }
    }

    private fun showLanguageSelectionDialog() {
        val dialog = Dialog(this)
        dialog.setContentView(R.layout.dialog_language_selection)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        val englishOption = dialog.findViewById<LinearLayout>(R.id.englishOption)
        val bengaliOption = dialog.findViewById<LinearLayout>(R.id.bengaliOption)
        val hindiOption = dialog.findViewById<LinearLayout>(R.id.hindiOption)

        val englishRadio = dialog.findViewById<RadioButton>(R.id.englishRadio)
        val bengaliRadio = dialog.findViewById<RadioButton>(R.id.bengaliRadio)
        val hindiRadio = dialog.findViewById<RadioButton>(R.id.hindiRadio)

        val cancelButton = dialog.findViewById<Button>(R.id.cancelButton)
        val okButton = dialog.findViewById<Button>(R.id.okButton)

        // Set current selection
        val currentLanguage = LanguageManager.getSelectedLanguage(this)
        when (currentLanguage) {
            LanguageManager.LANGUAGE_ENGLISH -> englishRadio.isChecked = true
            LanguageManager.LANGUAGE_BENGALI -> bengaliRadio.isChecked = true
            LanguageManager.LANGUAGE_HINDI -> hindiRadio.isChecked = true
        }

        var selectedLanguage = currentLanguage

        englishOption.setOnClickListener {
            englishRadio.isChecked = true
            bengaliRadio.isChecked = false
            hindiRadio.isChecked = false
            selectedLanguage = LanguageManager.LANGUAGE_ENGLISH
        }

        bengaliOption.setOnClickListener {
            englishRadio.isChecked = false
            bengaliRadio.isChecked = true
            hindiRadio.isChecked = false
            selectedLanguage = LanguageManager.LANGUAGE_BENGALI
        }

        hindiOption.setOnClickListener {
            englishRadio.isChecked = false
            bengaliRadio.isChecked = false
            hindiRadio.isChecked = true
            selectedLanguage = LanguageManager.LANGUAGE_HINDI
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        okButton.setOnClickListener {
            if (selectedLanguage != currentLanguage) {
                LanguageManager.setLocale(this, selectedLanguage)
                dialog.dismiss()
                // Restart the activity to apply language changes immediately
                LanguageManager.restartActivity(this)
            } else {
                dialog.dismiss()
            }
        }

        dialog.show()
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }
    
    private fun signOut() {
        try {
            Log.d(TAG, "Signing out user")
            
            // Sign out from Firebase
            FirebaseAuth.getInstance().signOut()
            
            // Sign out from Google
            val googleSignInClient = GoogleSignIn.getClient(this, 
                GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).build())
            googleSignInClient.signOut()
            
            // Clear user data but KEEP terms acceptance
            val sharedPreferences = getSharedPreferences("JUBuddyPrefs", MODE_PRIVATE)
            sharedPreferences.edit().apply {
                remove("userEmail")
                remove("userName")
                remove("authMethod")
                // Deliberately NOT clearing termsAccepted
                apply()
            }
            
            Log.d(TAG, "Sign out complete, redirecting to SignInActivity")
            
            // Return to the sign-in screen
            val intent = Intent(this, SignInActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error signing out: ${e.message}", e)
        }
    }
    
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }
    
    override fun onDestroy() {
        adManager.destroy()
        nativeAdManager.destroy()
        super.onDestroy()
    }
} 