<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_background"
    tools:context=".SettingsActivity">

    <!-- Header with back button and title -->
    <LinearLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/backButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_back"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="8dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings_title"
            android:textColor="@color/card_text"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <!-- Spacer to balance the layout -->
        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />
    </LinearLayout>

    <!-- Profile Section -->
    <LinearLayout
        android:id="@+id/profileSection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        app:layout_constraintTop_toBottomOf="@id/headerLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Profile Avatar -->
        <FrameLayout
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:background="@drawable/profile_avatar_background">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_profile_avatar" />
        </FrameLayout>

        <!-- Profile Name -->
        <TextView
            android:id="@+id/profileName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ethan Carter"
            android:textColor="@color/card_text"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp" />

        <!-- Profile Email -->
        <TextView
            android:id="@+id/profileEmail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="<EMAIL>"
            android:textColor="#888888"
            android:textSize="16sp"
            android:layout_marginTop="4dp" />
    </LinearLayout>

    <!-- Menu Items -->
    <LinearLayout
        android:id="@+id/menuItemsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/profileSection"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Language Menu Item -->
        <LinearLayout
            android:id="@+id/languageMenuItem"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/settings_menu_item_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="12dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_language"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/language"
                android:textColor="@color/card_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_forward" />
        </LinearLayout>

        <!-- Terms & Conditions Menu Item -->
        <LinearLayout
            android:id="@+id/termsMenuItem"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/settings_menu_item_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="12dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_terms"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/terms_conditions"
                android:textColor="@color/card_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_forward" />
        </LinearLayout>

        <!-- Sign Out Menu Item -->
        <LinearLayout
            android:id="@+id/signOutMenuItem"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/settings_menu_item_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="12dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_sign_out"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/sign_out"
                android:textColor="@color/card_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_forward" />
        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 