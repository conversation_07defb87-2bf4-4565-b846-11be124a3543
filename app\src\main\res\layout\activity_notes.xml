<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_background"
    android:padding="16dp">

    <TextView
        android:id="@+id/notesTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/select_notes_category"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/card_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="32dp" />

    <!-- First Row -->
    <LinearLayout
        android:id="@+id/firstRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintTop_toBottomOf="@id/notesTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="48dp">

        <LinearLayout
            android:id="@+id/scienceButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_science"
                android:layout_marginBottom="6dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/science"
                android:textColor="@color/card_text"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/artsButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_arts"
                android:layout_marginBottom="6dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/arts"
                android:textColor="@color/card_text"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- Second Row - Engineering Button -->
    <LinearLayout
        android:id="@+id/secondRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/firstRow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp">

        <LinearLayout
            android:id="@+id/engineeringButton"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1"
            android:background="@drawable/card_button_background"
            android:textColor="@color/card_text"
            android:textSize="12sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_margin="8dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground"
            android:orientation="vertical"
            android:maxWidth="200dp">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_engineering"
                android:layout_marginBottom="6dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/engineering"
                android:textColor="@color/card_text"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- Third Row - Back Button -->
    <LinearLayout
        android:id="@+id/thirdRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/secondRow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/adContainer"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="80dp">

        <LinearLayout
            android:id="@+id/backButton"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1"
            android:background="@drawable/card_button_background"
            android:textColor="@color/card_text"
            android:textSize="12sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_margin="8dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground"
            android:orientation="vertical"
            android:maxWidth="200dp">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_back"
                android:layout_marginBottom="6dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/back"
                android:textColor="@color/card_text"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 