<resources>
    <string name="app_name">JUBuddy AI</string>
    
    <!-- Sign In Screen -->
    <string name="hint_email">Email</string>
    <string name="hint_password">Password</string>
    <string name="btn_sign_in">Sign In</string>
    <string name="btn_sign_in_google">Sign in with Google</string>
    <string name="text_create_account">New user? Create an account</string>
    <string name="text_or">OR</string>
    <string name="error_invalid_email">Enter a valid email address</string>
    <string name="error_invalid_password">Password must be at least 6 characters</string>
    
    <!-- Chat Activity -->
    <string name="analyzing_image">Analyzing image...</string>
    <string name="analyzing_wait">Analyzing image, please wait...</string>
    <string name="ask_about_image">Ask about this image...</string>
    <string name="ask_about_text">Ask about this text...</string>
    <string name="type_message">Type a message...</string>
    <string name="error_processing_image">Error processing image: %1$s</string>

    <!-- Settings -->
    <string name="settings_title">Settings</string>
    <string name="language">Language</string>
    <string name="terms_conditions">Terms &amp; Conditions</string>
    <string name="sign_out">Sign Out</string>

    <!-- Main Activity -->
    <string name="home">Home</string>
    <string name="pyq">PYQ</string>
    <string name="syllabus">Syllabus</string>
    <string name="notes">NOTES</string>
    <string name="store">STORE</string>
    <string name="jubuddy_ai">JUBuddy AI</string>
    <string name="settings">SETTINGS</string>

    <!-- Notes Activity -->
    <string name="select_notes_category">Select Notes Category</string>
    <string name="science">SCIENCE</string>
    <string name="arts">ARTS</string>
    <string name="engineering">ENGINEERING</string>
    <string name="back">BACK</string>

    <!-- Language Selection -->
    <string name="select_language">Select Language</string>
    <string name="english">English</string>
    <string name="bengali">Bengali</string>
    <string name="hindi">Hindi</string>

    <!-- This will be automatically populated when you connect to Firebase -->
    <string name="default_web_client_id" translatable="false">38383343371-0rc699nkm1d0jkt8qbfpnmd16rrb6d0u.apps.googleusercontent.com</string>
</resources>