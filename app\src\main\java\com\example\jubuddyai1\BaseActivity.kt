package com.example.jubuddyai1

import android.content.Context
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.example.jubuddyai1.utils.LanguageManager

abstract class BaseActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Apply language before setting content view
        LanguageManager.applyLanguage(this)
    }
    
    override fun attachBaseContext(newBase: Context?) {
        if (newBase != null) {
            val selectedLanguage = LanguageManager.getSelectedLanguage(newBase)
            LanguageManager.setLocale(newBase, selectedLanguage)
        }
        super.attachBaseContext(newBase)
    }
}
