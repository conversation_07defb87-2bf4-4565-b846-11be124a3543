package com.example.jubuddyai1

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.jubuddyai1.ads.AdManager
import com.example.jubuddyai1.ads.NativeAdManager
import com.example.jubuddyai1.utils.LanguageManager
import com.google.android.material.button.MaterialButton

class MainActivity : BaseActivity() {
    
    private lateinit var adManager: AdManager
    private lateinit var nativeAdManager: NativeAdManager
    private val NATIVE_AD_ID = "ca-app-pub-3639460814030294/3631252078"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideSystemUI()
        setContentView(R.layout.activity_main)

        // Initialize AdManager
        adManager = AdManager(this)
        adManager.initialize()
        
        // Initialize the native ad manager
        nativeAdManager = NativeAdManager(this)
        
        // Load banner ad
        val adContainer = findViewById<FrameLayout>(R.id.adContainer)
        adManager.loadBannerAd(adContainer)
        
        // Load native ad
        nativeAdManager.loadNativeAd(adContainer, NATIVE_AD_ID)
        
        setupButtons()
    }

    override fun onResume() {
        super.onResume()
        // Check if language has changed and recreate if needed
        val currentLanguage = LanguageManager.getSelectedLanguage(this)
        val currentLocale = resources.configuration.locales[0].language
        if (currentLanguage != currentLocale) {
            LanguageManager.applyLanguage(this)
            recreate()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }

    private fun setupButtons() {
        findViewById<LinearLayout>(R.id.homeButton).setOnClickListener {
            val intent = Intent(this, WebViewActivity::class.java).apply {
                putExtra("url", "https://jadavpuruniversity.in/")
            }
            startActivity(intent)
        }

        findViewById<LinearLayout>(R.id.pyqButton).setOnClickListener {
            val intent = Intent(this, WebViewActivity::class.java).apply {
                putExtra("url", "http://136.232.79.176:9900/questionbank")
            }
            startActivity(intent)
        }

        findViewById<LinearLayout>(R.id.syllabusButton).setOnClickListener {
            val intent = Intent(this, WebViewActivity::class.java).apply {
                putExtra("url", "https://jadavpuruniversity.in/courses/")
            }
            startActivity(intent)
        }

        findViewById<LinearLayout>(R.id.notesButton).setOnClickListener {
            adManager.showInterstitialAd(this) {
                startActivity(Intent(this, NotesActivity::class.java))
            }
        }

        findViewById<LinearLayout>(R.id.storeButton).setOnClickListener {
            val intent = Intent(this, WebViewActivity::class.java).apply {
                putExtra("url", "https://ju-buddy.mini.site")
            }
            startActivity(intent)
        }

        findViewById<LinearLayout>(R.id.jubuddyButton).setOnClickListener {
            startActivity(Intent(this, ChatActivity::class.java))
        }

        findViewById<LinearLayout>(R.id.settingsButton).setOnClickListener {
            startActivity(Intent(this, SettingsActivity::class.java))
        }
    }

    override fun onDestroy() {
        adManager.destroy()
        nativeAdManager.destroy()
        super.onDestroy()
    }
}