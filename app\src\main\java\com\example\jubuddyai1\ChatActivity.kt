package com.example.jubuddyai1

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.speech.RecognizerIntent
import android.view.View
import android.widget.EditText
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import android.graphics.drawable.AnimationDrawable
import android.widget.ImageView
import android.widget.LinearLayout
import android.graphics.Color
import android.widget.ProgressBar
import android.content.Context
import com.example.jubuddyai1.EnhancedLoadingIndicator
import com.example.jubuddyai1.document.DocumentService
import com.example.jubuddyai1.service.BackendService
import java.util.regex.Pattern
import com.example.jubuddyai1.ads.AdManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import android.util.Log
import com.example.jubuddyai1.util.ConversationContext
import com.google.android.material.bottomsheet.BottomSheetDialog
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import android.os.Handler
import android.os.Looper
import android.content.res.Configuration

class ChatActivity : AppCompatActivity() {
    companion object {
        private const val STORAGE_PERMISSION_CODE = 1001
        private const val BACKEND_URL = "https://jubuddy-ai-backend.onrender.com"
    }
    private lateinit var messageInput: EditText
    private lateinit var sendButton: FloatingActionButton
    private lateinit var voiceButton: ImageButton
    private lateinit var plusButton: ImageButton
    private lateinit var recyclerView: RecyclerView
    private lateinit var loadingText: TextView
    private lateinit var adapter: ChatAdapter
    private val messages = mutableListOf<ChatMessage>()

    private val client = OkHttpClient()
    private lateinit var backendService: BackendService

    // Activity result contracts
    private val getImageContent = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let { processImage(it) }
    }

    private val getDocumentContent = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let { processDocument(it) }
    }

    private val takePicture = registerForActivityResult(ActivityResultContracts.TakePicture()) { success: Boolean ->
        if (success) {
            cameraImageUri?.let { processImage(it) }
        }
    }

    private val speechRecognizer = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            val spokenText = result.data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)?.get(0) ?: ""
            messageInput.setText(spokenText)
        }
    }

    private var loadingAnimation: AnimationDrawable? = null
    private lateinit var loadingIndicator: EnhancedLoadingIndicator
    private lateinit var welcomeContainer: LinearLayout
    private lateinit var welcomeImage: ImageView
    private lateinit var welcomeTitle: TextView
    private lateinit var welcomeSubtitle: TextView
    
    // Flag to track if we're currently processing a response
    private var isProcessingResponse = false

    private var pinnedImageUri: Uri? = null
    private var pinnedDocumentUri: Uri? = null
    private var cameraImageUri: Uri? = null

    // Add this property
    private lateinit var documentService: DocumentService
    
    // Add this to track the latest AI response for document generation
    private var latestAiContent: String = ""
    private var pendingDocumentFormat: String? = null
    private var pendingDocumentTitle: String? = null
    private var lastGeneratedFilePath: String? = null

    // Add these at the beginning with other class properties
    private lateinit var adManager: AdManager
    private var interactionCount = 0



    private lateinit var conversationContext: ConversationContext

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set system bars to dark theme
        window.statusBarColor = Color.BLACK
        window.navigationBarColor = Color.BLACK
        
        setContentView(R.layout.activity_chat)



        // Initialize document service
        documentService = DocumentService(applicationContext)

        // Initialize backend service
        backendService = BackendService(this)

        // Initialize AdManager
        adManager = AdManager(this)
        adManager.initialize()

        // Initialize ConversationContext
        conversationContext = ConversationContext(getSharedPreferences("chat_prefs", Context.MODE_PRIVATE))

        initializeViews()
        setupRecyclerView()
        setupClickListeners()

        // Load saved messages if available
        loadChatHistory()
        
        // Set the loaded messages to adapter
        adapter.setMessages(messages)
    }

    private fun initializeViews() {
        messageInput = findViewById(R.id.messageInput)
        sendButton = findViewById(R.id.sendButton)
        voiceButton = findViewById(R.id.voiceButton)
        plusButton = findViewById(R.id.plusButton)
        recyclerView = findViewById(R.id.recyclerView)
        loadingText = findViewById(R.id.loadingText)
        loadingIndicator = EnhancedLoadingIndicator(this)
        
        // Set up the stop generation listener
        loadingIndicator.setOnStopGenerationListener(object : EnhancedLoadingIndicator.OnStopGenerationListener {
            override fun onStopGeneration() {
                // Cancel the ongoing API request
                client.dispatcher.cancelAll()
                
                // Reset processing flag
                isProcessingResponse = false
                
                // Re-enable input controls
                setInputEnabled(true)
                
                // Add a message indicating generation was stopped
                val currentAiMessageIndex = messages.size - 1
                if (currentAiMessageIndex >= 0 && !messages[currentAiMessageIndex].isUser) {
                    // Add indication that generation was stopped
                    val currentContent = messages[currentAiMessageIndex].content
                    val updatedContent = if (currentContent.isEmpty()) {
                        "[Generation stopped]"
                    } else {
                        "$currentContent\n\n[Generation stopped]"
                    }
                    
                    messages[currentAiMessageIndex] = ChatMessage(updatedContent, false)
                    adapter.updateMessage(currentAiMessageIndex, messages[currentAiMessageIndex])
                }
                
                Toast.makeText(this@ChatActivity, "Generation stopped", Toast.LENGTH_SHORT).show()
            }
        })
        
        // Initialize the loading animation
        val drawable = loadingText.compoundDrawables[2] // Get the end drawable
        if (drawable is AnimationDrawable) {
            loadingAnimation = drawable
        }

        // Initialize welcome screen views
        welcomeContainer = findViewById(R.id.welcomeContainer)
        welcomeImage = findViewById(R.id.welcomeImage)
        welcomeTitle = findViewById(R.id.welcomeTitle) 
        welcomeSubtitle = findViewById(R.id.welcomeSubtitle)
    }

    private fun setupRecyclerView() {
        adapter = ChatAdapter()
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    private fun setupClickListeners() {
        sendButton.setOnClickListener {
            val message = messageInput.text.toString().trim()
            if (message.isNotEmpty()) {
                sendMessage(message)
                messageInput.text.clear()
            }
        }

        voiceButton.setOnClickListener {
            startVoiceRecognition()
        }

        plusButton.setOnClickListener {
            showAddContentBottomSheet()
        }

        // The stop generation button functionality is now handled by the EnhancedLoadingIndicator
    }

    private fun startVoiceRecognition() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
        }
        speechRecognizer.launch(intent)
    }

    private fun showAddContentBottomSheet() {
        val bottomSheetDialog = BottomSheetDialog(this)
        val view = layoutInflater.inflate(R.layout.bottom_sheet_add_content, null)
        bottomSheetDialog.setContentView(view)

        // Set up click listeners for each option
        view.findViewById<View>(R.id.imageOption).setOnClickListener {
            bottomSheetDialog.dismiss()
            checkAndRequestImagePermission()
        }

        view.findViewById<View>(R.id.documentOption).setOnClickListener {
            bottomSheetDialog.dismiss()
            checkAndRequestDocumentPermission()
        }

        view.findViewById<View>(R.id.cameraOption).setOnClickListener {
            bottomSheetDialog.dismiss()
            checkAndRequestCameraPermission()
        }

        bottomSheetDialog.show()
    }

    private fun checkAndRequestImagePermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                STORAGE_PERMISSION_CODE)
        } else {
            openImagePicker()
        }
    }

    private fun checkAndRequestDocumentPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                STORAGE_PERMISSION_CODE + 1)
        } else {
            openDocumentPicker()
        }
    }

    private fun checkAndRequestCameraPermission() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.CAMERA)
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }

        if (permissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, permissions.toTypedArray(), STORAGE_PERMISSION_CODE + 2)
        } else {
            openCamera()
        }
    }

    private fun openImagePicker() {
        getImageContent.launch("image/*")
    }

    private fun openDocumentPicker() {
        getDocumentContent.launch("*/*")
    }

    private fun openCamera() {
        val photoFile = File(getExternalFilesDir(null), "camera_image_${System.currentTimeMillis()}.jpg")
        cameraImageUri = androidx.core.content.FileProvider.getUriForFile(
            this,
            "${packageName}.fileprovider",
            photoFile
        )
        takePicture.launch(cameraImageUri)
    }

    private fun processImage(uri: Uri) {
        try {
            // Pin the image for display
            pinImage(uri)

            // Note: Text extraction will now be handled by the backend
            // when the user sends a message

        } catch (e: Exception) {
            Toast.makeText(
                this,
                getString(R.string.error_processing_image, e.message),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun processDocument(uri: Uri) {
        try {
            // Pin the document for display
            pinDocument(uri)

            // Note: Text extraction will now be handled by the backend
            // when the user sends a message

        } catch (e: Exception) {
            Toast.makeText(
                this,
                "Error processing document: ${e.message}",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun pinDocument(uri: Uri) {
        pinnedDocumentUri = uri

        // Get document name
        val documentName = getDocumentName(uri)

        // Show the pinned document above the input box
        val pinnedContentContainer = findViewById<View>(R.id.pinnedContentContainer)
        val pinnedDocumentCard = findViewById<View>(R.id.pinnedDocumentCard)
        val pinnedDocumentName = findViewById<TextView>(R.id.pinnedDocumentName)
        val pinnedDocumentPrompt = findViewById<TextView>(R.id.pinnedDocumentPrompt)
        val clearPinnedDocumentButton = findViewById<ImageView>(R.id.clearPinnedDocumentButton)
        val documentAnalysisProgress = findViewById<ProgressBar>(R.id.documentAnalysisProgress)

        // Show the containers with animation
        pinnedContentContainer.visibility = View.VISIBLE
        pinnedDocumentCard.alpha = 0f
        pinnedDocumentCard.visibility = View.VISIBLE
        pinnedDocumentCard.animate().alpha(1f).setDuration(200).start()

        // Set the document name
        pinnedDocumentName.text = documentName

        // Hide analysis progress and show ready state
        documentAnalysisProgress.visibility = View.GONE
        pinnedDocumentPrompt.text = "Ask about this document..."

        // Set up clear button
        clearPinnedDocumentButton.setOnClickListener {
            pinnedDocumentCard.animate().alpha(0f).setDuration(200).withEndAction {
                pinnedDocumentCard.visibility = View.GONE
                if (findViewById<View>(R.id.pinnedImageCard).visibility == View.GONE) {
                    pinnedContentContainer.visibility = View.GONE
                }
                pinnedDocumentUri = null
            }.start()
        }

        // Focus input field and show keyboard
        messageInput.requestFocus()
        messageInput.hint = "Ask about this document..."
    }

    private fun getDocumentName(uri: Uri): String {
        var name = "Document"
        contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
            if (nameIndex != -1 && cursor.moveToFirst()) {
                name = cursor.getString(nameIndex) ?: "Document"
            }
        }
        return name
    }



    private fun pinImage(uri: Uri) {
        pinnedImageUri = uri

        // Show the pinned image above the input box - using correct IDs from layout
        val pinnedContentContainer = findViewById<View>(R.id.pinnedContentContainer)
        val pinnedImageCard = findViewById<View>(R.id.pinnedImageCard)
        val pinnedImageView = findViewById<ImageView>(R.id.pinnedImageView)
        val pinnedImagePrompt = findViewById<TextView>(R.id.pinnedImagePrompt)
        val clearPinnedImageButton = findViewById<ImageView>(R.id.clearPinnedImageButton)
        val imageAnalysisProgress = findViewById<ProgressBar>(R.id.imageAnalysisProgress)

        // Show the containers with animation
        pinnedContentContainer.visibility = View.VISIBLE
        pinnedImageCard.alpha = 0f
        pinnedImageCard.visibility = View.VISIBLE
        pinnedImageCard.animate().alpha(1f).setDuration(200).start()

        // Set the image
        pinnedImageView.setImageURI(uri)

        // Hide analysis progress and show ready state
        imageAnalysisProgress.visibility = View.GONE
        pinnedImagePrompt.text = "Ask about this image..."

        // Set up clear button
        clearPinnedImageButton.setOnClickListener {
            pinnedImageCard.animate().alpha(0f).setDuration(200).withEndAction {
                pinnedImageCard.visibility = View.GONE
                if (findViewById<View>(R.id.pinnedDocumentCard).visibility == View.GONE) {
                    pinnedContentContainer.visibility = View.GONE
                }
                pinnedImageUri = null
            }.start()
        }

        // Focus input field and show keyboard
        messageInput.requestFocus()
        messageInput.hint = "Ask about this image..."
    }





    private fun detectDocumentRequest(message: String): Boolean {
        val documentPatterns = listOf(
            "(?:save|generate|create|make|export|convert)\\s+(?:this|a|an|the)?\\s*(?:as|to|into)?\\s*(?:a|an)?\\s*(pdf)\\b",
            "\\b(?:pdf)\\s+(?:file|document|version)\\b",
            "\\bsave\\s+(?:this|that|the|your|previous|last)?\\s+(?:response|answer|reply|message|text)\\b",
            "\\bcreate\\s+(?:a|an)?\\s+document\\b",
            "\\bgenerate\\s+(?:a|an)?\\s+document\\b"
        )
        
        return documentPatterns.any { Pattern.compile(it, Pattern.CASE_INSENSITIVE).matcher(message).find() }
    }
    
    private fun extractDocumentParams(message: String): Pair<String, String> {
        // Extract format
        val formatPattern = "\\b(pdf|csv|txt|text)\\b"
        val formatMatcher = Pattern.compile(formatPattern, Pattern.CASE_INSENSITIVE).matcher(message)
        val format = if (formatMatcher.find()) formatMatcher.group(1).lowercase() else "pdf"
        
        // Extract title if specified
        val titlePattern = "(?:titled|title|called|named)\\s+[\"']?([^\"']+)[\"']?"
        val titleMatcher = Pattern.compile(titlePattern, Pattern.CASE_INSENSITIVE).matcher(message)
        val title = if (titleMatcher.find()) 
            titleMatcher.group(1) 
        else 
            "JUBuddy_Document_${System.currentTimeMillis()}"
        
        return Pair(format, title)
    }
    
    private fun generateDocument(title: String, content: String, format: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            val result = documentService.generateDocument(title, content, format)
            
            withContext(Dispatchers.Main) {
                if (result.success && result.filePath != null) {
                    lastGeneratedFilePath = result.filePath
                    
                    val extension = when (format) {
                        "csv" -> "CSV"
                        "txt", "text" -> "TXT"
                        else -> "PDF"
                    }
                    
                    val successMessage = "I've created a $extension document titled \"$title\". " +
                            "Would you like to open it or share it?"
                    
                    // Add AI message
                    val aiMessage = ChatMessage(successMessage, false)
                    messages.add(aiMessage)
                    adapter.addMessage(aiMessage)
                    recyclerView.smoothScrollToPosition(messages.size - 1)
                } else {
                    val errorMessage = "I couldn't create the document. ${result.errorMessage ?: "An unknown error occurred."}"
                    
                    // Add AI message
                    val aiMessage = ChatMessage(errorMessage, false)
                    messages.add(aiMessage)
                    adapter.addMessage(aiMessage)
                }
            }
        }
    }
    
    private fun handleDocumentAction(message: String) {
        if (lastGeneratedFilePath == null) {
            val aiMessage = ChatMessage("I don't have a recently created document to open or share.", false)
            messages.add(aiMessage)
            adapter.addMessage(aiMessage)
            return
        }
        
        val openPattern = Pattern.compile("(?:open|view|show|display)\\s+(?:it|document|file)", Pattern.CASE_INSENSITIVE)
        val sharePattern = Pattern.compile("(?:share|send|email)\\s+(?:it|document|file)", Pattern.CASE_INSENSITIVE)
        
        val success = when {
            openPattern.matcher(message).find() -> documentService.openDocument(lastGeneratedFilePath!!)
            sharePattern.matcher(message).find() -> documentService.shareDocument(lastGeneratedFilePath!!)
            else -> false
        }
        
        val responseMessage = when {
            openPattern.matcher(message).find() && success -> "I've opened the document for you."
            sharePattern.matcher(message).find() && success -> "I've started sharing the document."
            openPattern.matcher(message).find() -> "I couldn't open the document. You may not have an app that can view this file type."
            sharePattern.matcher(message).find() -> "I couldn't share the document."
            else -> "Would you like to open or share the document? Just say 'open it' or 'share it'."
        }
        
        val aiMessage = ChatMessage(responseMessage, false)
        messages.add(aiMessage)
        adapter.addMessage(aiMessage)
    }

    // Modify sendMessage to handle document-related requests
    private fun sendMessage(message: String) {
        // Hide welcome screen if visible
        if (welcomeContainer.visibility == View.VISIBLE) {
            welcomeContainer.visibility = View.GONE
            recyclerView.visibility = View.VISIBLE
        }
        
        // Add user message to chat
        val chatMessage = ChatMessage(message, true, pinnedImageUri)
        messages.add(chatMessage)
        adapter.addMessage(chatMessage)
        
        // NEW: Check for agent commands first
        if (handleAgentCommands(message)) {
            return
        }
        
        // Check if this is a document request
        if (detectDocumentRequest(message)) {
            val (format, title) = extractDocumentParams(message)
            
            if (messages.size >= 2 && !messages[messages.size - 2].isUser) {
                // Use the previous AI response to generate document
                val content = messages[messages.size - 2].content
                generateDocument(title, content, format)
                return
            } else {
                // No previous AI response, set pending document and ask AI for content
                pendingDocumentFormat = format
                pendingDocumentTitle = title
                
                // Ask AI to generate content for document
                val docPrompt = "Please generate content for a $format document titled \"$title\". Be comprehensive and well-structured."
                
                // Regular API request without changing UI flow
                processApiRequest(docPrompt)
                return
            }
        }
        
        // Check if this is a document action (open/share)
        val actionPattern = Pattern.compile("(?:open|view|show|share|send)\\s+(?:it|document|file)", Pattern.CASE_INSENSITIVE)
        if (actionPattern.matcher(message).find() && lastGeneratedFilePath != null) {
            handleDocumentAction(message)
            return
        }
        
        // Regular message processing
        val isImageQuery = pinnedImageUri != null
        
        // Increment the interaction counter
        interactionCount++
        
        // Check if we should show an interstitial ad (every 2 interactions)
        if (interactionCount % 2 == 0) {
            // Show ad before continuing with the API request
            adManager.showInterstitialAd(this) {
                // Continue with the regular message processing after ad is dismissed
                processApiRequest(message)
            }
        } else {
            // Continue with regular message processing without showing an ad
            processApiRequest(message)
        }
    }
    
    // New method to separate the API request logic from the ad display logic
    private fun processApiRequest(message: String) {
        // Regular message processing
        val isImageQuery = pinnedImageUri != null
        val isDocumentQuery = pinnedDocumentUri != null

        // Show loading indicator with stop button
        loadingIndicator.setLoadingText("Generating response...")
        loadingIndicator.showWithStopButton(true)

        // Add an empty AI message that will be updated as streaming comes in
        val aiMessage = ChatMessage("", false)
        messages.add(aiMessage)
        adapter.addMessage(aiMessage)

        // Store the index of the AI message for later reference
        val currentAiMessageIndex = messages.size - 1

        // The typing animation is now handled by the ChatAdapter internally

        // Scroll to bottom only when starting
        recyclerView.smoothScrollToPosition(messages.size - 1)

        // Get conversation history for context
        val conversationHistory = getConversationHistory()

        // Send request to backend based on content type
        lifecycleScope.launch {
            try {
                val response = when {
                    isImageQuery -> {
                        backendService.sendImageQuery(message, pinnedImageUri!!, conversationHistory)
                    }
                    isDocumentQuery -> {
                        backendService.sendDocumentQuery(message, pinnedDocumentUri!!, conversationHistory)
                    }
                    else -> {
                        backendService.sendTextQuery(message, conversationHistory)
                    }
                }

                handleBackendResponse(response, currentAiMessageIndex)

            } catch (e: Exception) {
                runOnUiThread {
                    loadingIndicator.dismiss()
                    isProcessingResponse = false
                    setInputEnabled(true)

                    Toast.makeText(this@ChatActivity, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
                    messages.removeAt(currentAiMessageIndex)
                    adapter.notifyItemRemoved(currentAiMessageIndex)
                }
            }
        }

        // After creating the request, hide the pinned content
        val pinnedContentContainer = findViewById<View>(R.id.pinnedContentContainer)
        val pinnedImageCard = findViewById<View>(R.id.pinnedImageCard)
        val pinnedDocumentCard = findViewById<View>(R.id.pinnedDocumentCard)

        if (pinnedImageCard.visibility == View.VISIBLE) {
            pinnedImageCard.animate().alpha(0f).setDuration(200).withEndAction {
                pinnedImageCard.visibility = View.GONE
                if (pinnedDocumentCard.visibility == View.GONE) {
                    pinnedContentContainer.visibility = View.GONE
                }
            }.start()
        }

        if (pinnedDocumentCard.visibility == View.VISIBLE) {
            pinnedDocumentCard.animate().alpha(0f).setDuration(200).withEndAction {
                pinnedDocumentCard.visibility = View.GONE
                if (pinnedImageCard.visibility == View.GONE) {
                    pinnedContentContainer.visibility = View.GONE
                }
            }.start()
        }

        // Reset for next use
        pinnedImageUri = null
        pinnedDocumentUri = null
        messageInput.hint = "Type a message..."


    }

    // Helper method to get conversation history for backend
    private fun getConversationHistory(): List<Map<String, String>> {
        val history = mutableListOf<Map<String, String>>()

        // Get the last few messages for context (excluding the current user message)
        val recentMessages = messages.takeLast(6) // Last 3 exchanges

        for (message in recentMessages) {
            history.add(mapOf(
                "role" to (if (message.isUser) "user" else "assistant"),
                "content" to message.content
            ))
        }

        return history
    }

    // Handle response from backend
    private suspend fun handleBackendResponse(response: Response, currentAiMessageIndex: Int) {
        withContext(Dispatchers.Main) {
            try {
                if (!response.isSuccessful) {
                    val errorBody = response.body?.string()
                    loadingIndicator.dismiss()
                    isProcessingResponse = false
                    setInputEnabled(true)

                    Toast.makeText(this@ChatActivity, "Error: ${response.code} - ${errorBody ?: "No response"}", Toast.LENGTH_SHORT).show()
                    messages.removeAt(currentAiMessageIndex)
                    adapter.notifyItemRemoved(currentAiMessageIndex)
                    return@withContext
                }

                // Set flag to indicate we're processing a response
                isProcessingResponse = true
                setInputEnabled(false)

                // Process streaming response from backend
                response.body?.let { responseBody ->
                    val reader = responseBody.charStream().buffered()
                    val stringBuilder = StringBuilder()
                    var currentLine: String?

                    try {
                        while (true) {
                            currentLine = reader.readLine() ?: break

                            if (currentLine.isBlank() || !currentLine.startsWith("data:")) {
                                continue
                            }

                            val dataContent = currentLine.removePrefix("data:").trim()
                            if (dataContent == "[DONE]") {
                                break
                            }

                            try {
                                val jsonObject = JSONObject(dataContent)
                                val content = jsonObject.optString("content", "")

                                if (content.isNotEmpty()) {
                                    stringBuilder.append(content)

                                    // Update message content progressively
                                    adapter.updateMessage(currentAiMessageIndex, ChatMessage(stringBuilder.toString(), false))

                                    // Store latest content for document generation
                                    latestAiContent = stringBuilder.toString()

                                    // Auto-scroll to keep up with new content
                                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                    if (lastVisiblePosition >= messages.size - 2) {
                                        recyclerView.scrollToPosition(messages.size - 1)
                                    }
                                }
                            } catch (e: Exception) {
                                // Skip malformed JSON
                                continue
                            }
                        }
                    } finally {
                        // Finalize the response
                        loadingIndicator.dismiss()
                        isProcessingResponse = false

                        // Apply final formatting
                        val finalContent = stringBuilder.toString()
                        val finalMessage = ChatMessage(finalContent, false)
                        messages[currentAiMessageIndex] = finalMessage

                        Handler(Looper.getMainLooper()).postDelayed({
                            adapter.updateMessage(currentAiMessageIndex, finalMessage)
                            recyclerView.smoothScrollToPosition(messages.size - 1)
                        }, 200)

                        // Re-enable input controls
                        setInputEnabled(true)

                        // Store interaction in context
                        val userMessage = messages[messages.size - 2]
                        val botMessage = messages[messages.size - 1]
                        conversationContext.addInteraction(userMessage, botMessage)

                        // Check if we need to generate a document
                        if (pendingDocumentFormat != null && pendingDocumentTitle != null) {
                            generateDocument(pendingDocumentTitle!!, latestAiContent, pendingDocumentFormat!!)
                            pendingDocumentFormat = null
                            pendingDocumentTitle = null
                        }
                    }
                }

            } catch (e: Exception) {
                loadingIndicator.dismiss()
                isProcessingResponse = false
                setInputEnabled(true)

                Toast.makeText(this@ChatActivity, "Error processing response: ${e.message}", Toast.LENGTH_SHORT).show()
                messages.removeAt(currentAiMessageIndex)
                adapter.notifyItemRemoved(currentAiMessageIndex)
            }
        }
    }



    // Override onSaveInstanceState to save chat history
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        
        // Save the number of messages
        outState.putInt("MESSAGES_COUNT", messages.size)
        
        // Save each message individually
        messages.forEachIndexed { index, message ->
            outState.putString("MESSAGE_CONTENT_$index", message.content)
            outState.putBoolean("MESSAGE_IS_USER_$index", message.isUser)
            message.imageUri?.let { uri ->
                outState.putString("MESSAGE_IMAGE_URI_$index", uri.toString())
            }
        }
    }
    
    // Override onRestoreInstanceState to restore chat history
    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        
        // Get the number of messages
        val messagesCount = savedInstanceState.getInt("MESSAGES_COUNT", 0)
        
        // Clear existing messages
        messages.clear()
        
        // Restore each message
        for (i in 0 until messagesCount) {
            val content = savedInstanceState.getString("MESSAGE_CONTENT_$i") ?: ""
            val isUser = savedInstanceState.getBoolean("MESSAGE_IS_USER_$i", true)
            val imageUriString = savedInstanceState.getString("MESSAGE_IMAGE_URI_$i")
            val imageUri = if (imageUriString != null) Uri.parse(imageUriString) else null
            
            // Add the restored message
            messages.add(ChatMessage(content, isUser, imageUri))
        }
        
        // Update the adapter with the restored messages
        adapter.setMessages(messages)
    }
    
    // Add this method to handle configuration changes directly
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // No need to do anything special here since we're handling state in onSaveInstanceState
    }

    // Make sure to clean up animations in onDestroy
    override fun onDestroy() {
        saveChatHistory()
        adapter.clearAnimations()
        loadingIndicator.dismiss()
        if (::adManager.isInitialized) {
            adManager.destroy()
        }
        super.onDestroy()
    }

    // Add this helper method to enable/disable input controls
    private fun setInputEnabled(enabled: Boolean) {
        messageInput.isEnabled = enabled
        sendButton.isEnabled = enabled
        plusButton.isEnabled = enabled
        voiceButton.isEnabled = enabled

        // Optionally show visual indication
        messageInput.alpha = if (enabled) 1.0f else 0.5f
        sendButton.alpha = if (enabled) 1.0f else 0.5f
        plusButton.alpha = if (enabled) 1.0f else 0.5f
        voiceButton.alpha = if (enabled) 1.0f else 0.5f
        
        // If disabled, show a message or indicator
        if (!enabled) {
            Toast.makeText(this, "Please wait while the bot is generating a response...", Toast.LENGTH_SHORT).show()
        }
    }

    // Add a method to handle agent-style commands
    private fun handleAgentCommands(message: String): Boolean {
        // Check if it's a document creation request
        if (detectDocumentRequest(message)) {
            val title = extractDocumentTitle(message) ?: "JUBuddy_Document_${System.currentTimeMillis()}"
            
            // Find content to save - either specified in message or use previous AI response
            val contentToSave = if (messages.size >= 2 && !messages[messages.size - 2].isUser) {
                // Use the previous AI response
                messages[messages.size - 2].content
            } else if (latestAiContent.isNotEmpty()) {
                // Use the latest AI response if available
                latestAiContent
            } else {
                // No content available
                val aiMessage = ChatMessage("I don't have any content to save as a PDF. Please provide some text or ask me a question first.", false)
                messages.add(aiMessage)
                adapter.addMessage(aiMessage)
                return true
            }
            
            // Generate PDF
            generateDocument(title, contentToSave, "pdf")
            return true
        }
        
        return false
    }

    // Extract document title from user message
    private fun extractDocumentTitle(message: String): String? {
        val titlePatterns = listOf(
            "(?:titled|title|called|named)\\s+[\"']?([^\"']+)[\"']?",
            "with\\s+(?:the)?\\s+title\\s+[\"']?([^\"']+)[\"']?",
            "name(?:d|ing)\\s+(?:it|the\\s+document)\\s+[\"']?([^\"']+)[\"']?",
            "save\\s+(?:as|with)\\s+(?:the)?\\s+title\\s+[\"']?([^\"']+)[\"']?"
        )
        
        for (pattern in titlePatterns) {
            val matcher = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE).matcher(message)
            if (matcher.find()) {
                return matcher.group(1).trim()
            }
        }
        
        return null
    }

    override fun onPause() {
        super.onPause()
        // Save current chat history
        saveChatHistory()
    }
    
    private fun saveChatHistory() {
        val prefs = getSharedPreferences("chat_prefs", Context.MODE_PRIVATE)
        val editor = prefs.edit()
        
        // Convert messages to JSON string
        val gson = Gson()
        val messagesJson = gson.toJson(messages)  // Use messages list directly
        
        // Save to SharedPreferences
        editor.putString("chat_history", messagesJson)
        editor.apply()
        
        Log.d("ChatActivity", "Saved ${messages.size} messages to SharedPreferences")
    }
    
    private fun loadChatHistory() {
        val prefs = getSharedPreferences("chat_prefs", Context.MODE_PRIVATE)
        val messagesJson = prefs.getString("chat_history", null)
        
        if (messagesJson != null) {
            try {
                val gson = Gson()
                val type = object : TypeToken<List<ChatMessage>>() {}.type
                val loadedMessages = gson.fromJson<List<ChatMessage>>(messagesJson, type)
                
                // Clear existing messages and add loaded ones
                messages.clear()
                messages.addAll(loadedMessages)
                
                // Update adapter with loaded messages
                adapter.setMessages(messages)
                
                // Hide welcome screen if we have messages
                if (messages.isNotEmpty()) {
                    welcomeContainer.visibility = View.GONE
                    recyclerView.visibility = View.VISIBLE
                }
                
                Log.d("ChatActivity", "Loaded ${messages.size} messages from SharedPreferences")
            } catch (e: Exception) {
                Log.e("ChatActivity", "Error loading chat history", e)
            }
        }
    }

    // Add these override methods to ensure persistence
    override fun onStop() {
        super.onStop()
        saveChatHistory()
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            STORAGE_PERMISSION_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openImagePicker()
                } else {
                    Toast.makeText(this, "Storage permission is required to select images", Toast.LENGTH_SHORT).show()
                }
            }
            STORAGE_PERMISSION_CODE + 1 -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openDocumentPicker()
                } else {
                    Toast.makeText(this, "Storage permission is required to select documents", Toast.LENGTH_SHORT).show()
                }
            }
            STORAGE_PERMISSION_CODE + 2 -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    openCamera()
                } else {
                    Toast.makeText(this, "Camera and storage permissions are required to take photos", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
}
