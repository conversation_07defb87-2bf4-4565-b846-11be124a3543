package com.example.jubuddyai1

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.button.MaterialButton
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.example.jubuddyai1.ads.AdManager
import com.example.jubuddyai1.ads.NativeAdManager
import com.example.jubuddyai1.utils.LanguageManager

class NotesActivity : BaseActivity() {
    private lateinit var adManager: AdManager
    private lateinit var nativeAdManager: NativeAdManager
    private val NATIVE_AD_ID = "ca-app-pub-3639460814030294/3631252078"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideSystemUI()
        setContentView(R.layout.activity_notes)

        // Initialize AdManager
        adManager = AdManager(this)
        adManager.initialize()
        
        // Load banner ad
        val adContainer = findViewById<FrameLayout>(R.id.adContainer)
        adManager.loadBannerAd(adContainer)

        // Initialize the native ad manager
        nativeAdManager = NativeAdManager(this)
        
        // Load native ad
        nativeAdManager.loadNativeAd(adContainer, NATIVE_AD_ID)

        setupButtons()
    }

    override fun onResume() {
        super.onResume()
        // Check if language has changed and recreate if needed
        val currentLanguage = LanguageManager.getSelectedLanguage(this)
        val currentLocale = resources.configuration.locales[0].language
        if (currentLanguage != currentLocale) {
            LanguageManager.applyLanguage(this)
            recreate()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }

    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }

    private fun setupButtons() {
        findViewById<LinearLayout>(R.id.scienceButton).setOnClickListener {
            // Show interstitial ad first, then open WebView
            adManager.showInterstitialAd(this) {
                // This will be executed after ad is dismissed or if no ad is available
                val intent = Intent(this, WebViewActivity::class.java).apply {
                    putExtra("url", "https://drive.google.com/drive/folders/1--rKsfwBy2k0j6Uwlrg5spOyHuUdQEFG")
                }
                startActivity(intent)
            }
        }

        findViewById<LinearLayout>(R.id.artsButton).setOnClickListener {
            // Show interstitial ad first, then open WebView
            adManager.showInterstitialAd(this) {
                // This will be executed after ad is dismissed or if no ad is available
                val intent = Intent(this, WebViewActivity::class.java).apply {
                    putExtra("url", "https://drive.google.com/drive/folders/1-2ndupsc0wnvOvdiE3rAM5KkVcQNtuQW")
                }
                startActivity(intent)
            }
        }

        findViewById<LinearLayout>(R.id.engineeringButton).setOnClickListener {
            // Show interstitial ad first, then open WebView
            adManager.showInterstitialAd(this) {
                // This will be executed after ad is dismissed or if no ad is available
                val intent = Intent(this, WebViewActivity::class.java).apply {
                    putExtra("url", "https://drive.google.com/drive/folders/1-B6THqLPaKUKp1dR4Y_C1iyEsxgK9G2p")
                }
                startActivity(intent)
            }
        }


    }

    override fun onDestroy() {
        adManager.destroy()
        nativeAdManager.destroy()
        super.onDestroy()
    }
} 