package com.example.jubuddyai1.service

import android.content.Context
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.tasks.await
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import org.json.JSONArray
import java.io.File
import java.io.IOException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser

class BackendService(private val context: Context) {
    companion object {
        private const val TAG = "BackendService"
        private const val BACKEND_URL = "https://jubuddy-ai-backend.onrender.com"
        private const val TIMEOUT_SECONDS = 60L
    }

    private val client = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(300L, java.util.concurrent.TimeUnit.SECONDS) // Longer read timeout for streaming
        .writeTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .build()

    private val auth: FirebaseAuth = FirebaseAuth.getInstance()

    /**
     * Get Firebase ID token for the currently logged in user
     */
    private suspend fun getFirebaseIdToken(): String? = withContext(Dispatchers.IO) {
        return@withContext try {
            val currentUser: FirebaseUser? = auth.currentUser
            if (currentUser != null) {
                Log.d(TAG, "Getting ID token for user: ${currentUser.email}")
                val tokenResult = currentUser.getIdToken(false).await()
                val token = tokenResult.token
                Log.d(TAG, "Successfully retrieved ID token")
                token
            } else {
                Log.w(TAG, "No current user found, cannot get ID token")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get Firebase ID token", e)
            null
        }
    }

    /**
     * Add authentication header to request builder if token is available
     */
    private suspend fun addAuthHeader(requestBuilder: Request.Builder): Request.Builder {
        val token = getFirebaseIdToken()
        return if (token != null) {
            Log.d(TAG, "Adding Authorization header with Firebase ID token")
            requestBuilder.addHeader("Authorization", "Bearer $token")
        } else {
            Log.w(TAG, "No Firebase ID token available, proceeding without authentication")
            requestBuilder
        }
    }

    /**
     * Check if user is currently authenticated
     */
    fun isUserAuthenticated(): Boolean {
        val currentUser = auth.currentUser
        val isAuthenticated = currentUser != null
        Log.d(TAG, "User authentication status: $isAuthenticated, User: ${currentUser?.email}")
        return isAuthenticated
    }

    /**
     * Get current user information
     */
    fun getCurrentUser(): FirebaseUser? {
        return auth.currentUser
    }

    /**
     * Handle authentication errors in API responses
     */
    fun handleAuthenticationError(response: Response): Boolean {
        return when (response.code) {
            401 -> {
                Log.w(TAG, "Authentication failed - 401 Unauthorized")
                true
            }
            403 -> {
                Log.w(TAG, "Access forbidden - 403 Forbidden")
                true
            }
            else -> false
        }
    }

    /**
     * Send a text-only query to the backend
     */
    suspend fun sendTextQuery(
        message: String,
        conversationHistory: List<Map<String, String>> = emptyList()
    ): Response = withContext(Dispatchers.IO) {
        val requestBody = JSONObject().apply {
            put("query", message)  // Changed from "message" to "query"
            put("type", "text")
            put("stream", true)  // Enable streaming response
            if (conversationHistory.isNotEmpty()) {
                put("history", conversationHistory)
            }
        }

        val requestBuilder = Request.Builder()
            .url("$BACKEND_URL/api/chat")
            .post(requestBody.toString().toRequestBody("application/json".toMediaType()))
            .addHeader("Content-Type", "application/json")

        // Add Firebase authentication header
        val authenticatedRequestBuilder = addAuthHeader(requestBuilder)
        val request = authenticatedRequestBuilder.build()

        Log.d(TAG, "Sending text query to backend: $message")
        Log.d(TAG, "Request body: ${requestBody.toString()}")
        Log.d(TAG, "Request headers: ${request.headers}")

        val response = client.newCall(request).execute()
        Log.d(TAG, "Response code: ${response.code}")
        Log.d(TAG, "Response headers: ${response.headers}")
        Log.d(TAG, "Content-Type: ${response.header("Content-Type")}")
        Log.d(TAG, "Transfer-Encoding: ${response.header("Transfer-Encoding")}")

        // Log a preview of the response body for debugging (without consuming it)
        val responseBodyString = response.peekBody(1024).string()
        Log.d(TAG, "Response body preview (first 1024 chars): $responseBodyString")

        response
    }

    /**
     * Send a query with an image file to the backend
     */
    suspend fun sendImageQuery(
        message: String,
        imageUri: Uri,
        conversationHistory: List<Map<String, String>> = emptyList()
    ): Response = withContext(Dispatchers.IO) {
        val imageFile = createTempFileFromUri(imageUri, "image")

        // Determine proper MIME type based on file extension
        val mimeType = when (imageFile.extension.lowercase()) {
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            else -> "image/*"
        }

        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("query", message)  // Changed from "message" to "query"
            .addFormDataPart("type", "image")
            .addFormDataPart("stream", "true")  // Enable streaming response
            .addFormDataPart(
                "file",
                imageFile.name,
                imageFile.asRequestBody(mimeType.toMediaType())
            )
            .apply {
                if (conversationHistory.isNotEmpty()) {
                    // Convert history to proper JSON string
                    val historyJson = org.json.JSONArray(conversationHistory).toString()
                    addFormDataPart("history", historyJson)
                }
            }
            .build()

        val requestBuilder = Request.Builder()
            .url("$BACKEND_URL/api/chat")
            .post(requestBody)

        // Add Firebase authentication header
        val authenticatedRequestBuilder = addAuthHeader(requestBuilder)
        val request = authenticatedRequestBuilder.build()

        Log.d(TAG, "Sending image query to backend: $message")
        Log.d(TAG, "Image file: ${imageFile.name}, MIME type: $mimeType")
        try {
            client.newCall(request).execute()
        } finally {
            // Clean up temp file
            imageFile.delete()
        }
    }

    /**
     * Send a query with a document file to the backend
     */
    suspend fun sendDocumentQuery(
        message: String,
        documentUri: Uri,
        conversationHistory: List<Map<String, String>> = emptyList()
    ): Response = withContext(Dispatchers.IO) {
        val documentFile = createTempFileFromUri(documentUri, "document")

        // Determine proper MIME type based on file extension
        val mimeType = when (documentFile.extension.lowercase()) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            else -> "application/octet-stream"
        }

        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("query", message)  // Changed from "message" to "query"
            .addFormDataPart("type", "document")
            .addFormDataPart("stream", "true")  // Enable streaming response
            .addFormDataPart(
                "file",
                documentFile.name,
                documentFile.asRequestBody(mimeType.toMediaType())
            )
            .apply {
                if (conversationHistory.isNotEmpty()) {
                    // Convert history to proper JSON string
                    val historyJson = org.json.JSONArray(conversationHistory).toString()
                    addFormDataPart("history", historyJson)
                }
            }
            .build()

        val requestBuilder = Request.Builder()
            .url("$BACKEND_URL/api/chat")
            .post(requestBody)

        // Add Firebase authentication header
        val authenticatedRequestBuilder = addAuthHeader(requestBuilder)
        val request = authenticatedRequestBuilder.build()

        Log.d(TAG, "Sending document query to backend: $message")
        Log.d(TAG, "Document file: ${documentFile.name}, MIME type: $mimeType")
        try {
            client.newCall(request).execute()
        } finally {
            // Clean up temp file
            documentFile.delete()
        }
    }

    /**
     * Create a temporary file from URI for uploading
     */
    private fun createTempFileFromUri(uri: Uri, prefix: String): File {
        val inputStream = context.contentResolver.openInputStream(uri)
            ?: throw IOException("Cannot open input stream for URI: $uri")

        // Get the original file name and extension
        val originalFileName = getFileName(uri)
        val extension = originalFileName?.substringAfterLast('.', "") ?: ""
        val suffix = if (extension.isNotEmpty()) ".$extension" else null

        val tempFile = File.createTempFile(prefix, suffix, context.cacheDir)
        tempFile.outputStream().use { outputStream ->
            inputStream.use { input ->
                input.copyTo(outputStream)
            }
        }

        return tempFile
    }

    /**
     * Get the file name from URI
     */
    private fun getFileName(uri: Uri): String? {
        var fileName: String? = null

        // Try to get the file name from the URI
        context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (nameIndex != -1) {
                    fileName = cursor.getString(nameIndex)
                }
            }
        }

        // Fallback to the last path segment
        return fileName ?: uri.lastPathSegment
    }
}
