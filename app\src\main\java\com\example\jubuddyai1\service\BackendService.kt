package com.example.jubuddyai1.service

import android.content.Context
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.File
import java.io.IOException

class BackendService(private val context: Context) {
    companion object {
        private const val TAG = "BackendService"
        private const val BACKEND_URL = "https://jubuddy-ai-backend.onrender.com"
        private const val TIMEOUT_SECONDS = 60L
    }

    private val client = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, java.util.concurrent.TimeUnit.SECONDS)
        .build()

    /**
     * Send a text-only query to the backend
     */
    suspend fun sendTextQuery(
        message: String,
        conversationHistory: List<Map<String, String>> = emptyList()
    ): Response = withContext(Dispatchers.IO) {
        val requestBody = JSONObject().apply {
            put("message", message)
            put("type", "text")
            if (conversationHistory.isNotEmpty()) {
                put("history", conversationHistory)
            }
        }

        val request = Request.Builder()
            .url("$BACKEND_URL/api/chat")
            .post(requestBody.toString().toRequestBody("application/json".toMediaType()))
            .addHeader("Content-Type", "application/json")
            .build()

        Log.d(TAG, "Sending text query to backend: $message")
        client.newCall(request).execute()
    }

    /**
     * Send a query with an image file to the backend
     */
    suspend fun sendImageQuery(
        message: String,
        imageUri: Uri,
        conversationHistory: List<Map<String, String>> = emptyList()
    ): Response = withContext(Dispatchers.IO) {
        val imageFile = createTempFileFromUri(imageUri, "image")
        
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("message", message)
            .addFormDataPart("type", "image")
            .addFormDataPart(
                "file", 
                imageFile.name,
                imageFile.asRequestBody("image/*".toMediaType())
            )
            .apply {
                if (conversationHistory.isNotEmpty()) {
                    addFormDataPart("history", conversationHistory.toString())
                }
            }
            .build()

        val request = Request.Builder()
            .url("$BACKEND_URL/api/chat")
            .post(requestBody)
            .build()

        Log.d(TAG, "Sending image query to backend: $message")
        try {
            client.newCall(request).execute()
        } finally {
            // Clean up temp file
            imageFile.delete()
        }
    }

    /**
     * Send a query with a document file to the backend
     */
    suspend fun sendDocumentQuery(
        message: String,
        documentUri: Uri,
        conversationHistory: List<Map<String, String>> = emptyList()
    ): Response = withContext(Dispatchers.IO) {
        val documentFile = createTempFileFromUri(documentUri, "document")
        
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("message", message)
            .addFormDataPart("type", "document")
            .addFormDataPart(
                "file", 
                documentFile.name,
                documentFile.asRequestBody("application/octet-stream".toMediaType())
            )
            .apply {
                if (conversationHistory.isNotEmpty()) {
                    addFormDataPart("history", conversationHistory.toString())
                }
            }
            .build()

        val request = Request.Builder()
            .url("$BACKEND_URL/api/chat")
            .post(requestBody)
            .build()

        Log.d(TAG, "Sending document query to backend: $message")
        try {
            client.newCall(request).execute()
        } finally {
            // Clean up temp file
            documentFile.delete()
        }
    }

    /**
     * Create a temporary file from URI for uploading
     */
    private fun createTempFileFromUri(uri: Uri, prefix: String): File {
        val inputStream = context.contentResolver.openInputStream(uri)
            ?: throw IOException("Cannot open input stream for URI: $uri")
        
        val tempFile = File.createTempFile(prefix, null, context.cacheDir)
        tempFile.outputStream().use { outputStream ->
            inputStream.use { input ->
                input.copyTo(outputStream)
            }
        }
        
        return tempFile
    }

    /**
     * Get the file name from URI
     */
    private fun getFileName(uri: Uri): String {
        var fileName = "unknown"
        context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (nameIndex >= 0) {
                    fileName = cursor.getString(nameIndex) ?: "unknown"
                }
            }
        }
        return fileName
    }
}
