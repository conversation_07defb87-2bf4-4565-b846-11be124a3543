<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/card_background"
    android:padding="24dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_language"
        android:textColor="@color/card_text"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:id="@+id/englishOption"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:padding="16dp"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/english"
            android:textColor="@color/card_text"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/englishRadio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/bengaliOption"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:padding="16dp"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/bengali"
            android:textColor="@color/card_text"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/bengaliRadio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/hindiOption"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/hindi"
            android:textColor="@color/card_text"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/hindiRadio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancel"
            android:textColor="@color/card_text"
            android:background="?android:attr/selectableItemBackground"
            android:layout_marginEnd="16dp" />

        <Button
            android:id="@+id/okButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="OK"
            android:textColor="@color/card_text"
            android:background="?android:attr/selectableItemBackground" />
    </LinearLayout>
</LinearLayout>
